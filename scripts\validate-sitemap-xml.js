/**
 * XML Sitemap Validation Script
 * Validates the sitemap.xml file for proper XML format and structure
 */

const fs = require('fs')
const path = require('path')

// XML validation function
function validateXML(xmlContent) {
  const errors = []
  
  // Check XML declaration
  if (!xmlContent.startsWith('<?xml version="1.0" encoding="UTF-8"?>')) {
    errors.push('Missing or incorrect XML declaration')
  }
  
  // Check for XSL stylesheet
  if (!xmlContent.includes('<?xml-stylesheet type="text/xsl" href="/sitemap.xsl"?>')) {
    errors.push('Missing XSL stylesheet declaration')
  }
  
  // Check for required namespaces
  if (!xmlContent.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
    errors.push('Missing sitemap namespace')
  }
  
  if (!xmlContent.includes('xmlns:xhtml="http://www.w3.org/1999/xhtml"')) {
    errors.push('Missing xhtml namespace')
  }
  
  // Check for proper URL structure
  const urlMatches = xmlContent.match(/<url>/g)
  const urlCloseMatches = xmlContent.match(/<\/url>/g)
  
  if (!urlMatches || !urlCloseMatches || urlMatches.length !== urlCloseMatches.length) {
    errors.push('Mismatched URL tags')
  }
  
  // Check for required elements in each URL
  const locMatches = xmlContent.match(/<loc>.*?<\/loc>/g)
  const lastmodMatches = xmlContent.match(/<lastmod>.*?<\/lastmod>/g)
  const changefreqMatches = xmlContent.match(/<changefreq>.*?<\/changefreq>/g)
  const priorityMatches = xmlContent.match(/<priority>.*?<\/priority>/g)
  
  if (!locMatches || locMatches.length === 0) {
    errors.push('No location elements found')
  }
  
  if (!lastmodMatches || lastmodMatches.length === 0) {
    errors.push('No lastmod elements found')
  }
  
  if (!changefreqMatches || changefreqMatches.length === 0) {
    errors.push('No changefreq elements found')
  }
  
  if (!priorityMatches || priorityMatches.length === 0) {
    errors.push('No priority elements found')
  }
  
  // Check for hreflang links
  const hreflangMatches = xmlContent.match(/<xhtml:link rel="alternate" hreflang=".*?" href=".*?" \/>/g)
  if (!hreflangMatches || hreflangMatches.length === 0) {
    errors.push('No hreflang links found')
  }
  
  return errors
}

// MIME type validation
function validateMimeType() {
  const suggestions = [
    'Ensure server returns Content-Type: application/xml; charset=utf-8',
    'Add X-Content-Type-Options: nosniff header',
    'Include proper XML processing instructions',
    'Use XSL stylesheet for better browser display'
  ]
  
  return suggestions
}

// Main validation function
function validateSitemap() {
  const sitemapPath = path.join(__dirname, '../static/sitemap.xml')
  
  console.log('🔍 Validating XML Sitemap...\n')
  
  try {
    // Check if file exists
    if (!fs.existsSync(sitemapPath)) {
      console.error('❌ Sitemap file not found:', sitemapPath)
      return false
    }
    
    // Read and validate XML content
    const xmlContent = fs.readFileSync(sitemapPath, 'utf8')
    const errors = validateXML(xmlContent)
    
    // Display results
    if (errors.length === 0) {
      console.log('✅ XML Structure: Valid')
    } else {
      console.log('❌ XML Structure: Invalid')
      errors.forEach(error => console.log(`   - ${error}`))
    }
    
    // Count URLs
    const urlMatches = xmlContent.match(/<url>/g)
    const urlCount = urlMatches ? urlMatches.length : 0
    console.log(`📊 Total URLs: ${urlCount}`)
    
    // Count languages
    const hreflangMatches = xmlContent.match(/hreflang="([^"]+)"/g)
    const languages = new Set()
    if (hreflangMatches) {
      hreflangMatches.forEach(match => {
        const lang = match.match(/hreflang="([^"]+)"/)[1]
        if (lang !== 'x-default') {
          languages.add(lang)
        }
      })
    }
    console.log(`🌐 Languages: ${languages.size} (${Array.from(languages).join(', ')})`)
    
    // File size
    const stats = fs.statSync(sitemapPath)
    console.log(`📁 File Size: ${(stats.size / 1024).toFixed(2)} KB`)
    
    // MIME type suggestions
    console.log('\n📋 MIME Type Configuration:')
    const suggestions = validateMimeType()
    suggestions.forEach(suggestion => console.log(`   ✓ ${suggestion}`))
    
    // Browser compatibility notes
    console.log('\n🌐 Browser Compatibility:')
    console.log('   ✓ XML declaration with UTF-8 encoding')
    console.log('   ✓ XSL stylesheet for formatted display')
    console.log('   ✓ Proper namespace declarations')
    console.log('   ✓ Valid sitemap protocol structure')
    
    // Test URLs
    console.log('\n🔗 Sample URLs:')
    const locMatches = xmlContent.match(/<loc>(.*?)<\/loc>/g)
    if (locMatches) {
      locMatches.slice(0, 3).forEach(match => {
        const url = match.replace(/<\/?loc>/g, '')
        console.log(`   - ${url}`)
      })
      if (locMatches.length > 3) {
        console.log(`   ... and ${locMatches.length - 3} more`)
      }
    }
    
    console.log('\n✅ Sitemap validation completed!')
    return errors.length === 0
    
  } catch (error) {
    console.error('❌ Error validating sitemap:', error.message)
    return false
  }
}

// Run validation if script is executed directly
if (require.main === module) {
  validateSitemap()
}

module.exports = { validateSitemap, validateXML, validateMimeType }
