/**
 * Test Sitemap HTTP Headers
 * Tests the sitemap.xml endpoint to verify proper MIME type and headers
 */

const http = require('http')

function testSitemapHeaders(host = 'localhost', port = 3000) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: host,
      port: port,
      path: '/sitemap.xml',
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    }

    console.log(`🔍 Testing sitemap headers at http://${host}:${port}/sitemap.xml\n`)

    const req = http.request(options, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        console.log('📊 Response Status:', res.statusCode)
        console.log('📋 Response Headers:')
        
        Object.keys(res.headers).forEach(header => {
          console.log(`   ${header}: ${res.headers[header]}`)
        })

        console.log('\n🔍 Content Analysis:')
        console.log(`   Content Length: ${data.length} bytes`)
        console.log(`   Starts with XML declaration: ${data.startsWith('<?xml')}`)
        console.log(`   Contains XSL stylesheet: ${data.includes('<?xml-stylesheet')}`)
        console.log(`   Contains sitemap namespace: ${data.includes('http://www.sitemaps.org/schemas/sitemap/0.9')}`)

        // Check MIME type
        const contentType = res.headers['content-type']
        console.log('\n✅ MIME Type Validation:')
        
        if (contentType && contentType.includes('application/xml')) {
          console.log('   ✓ Correct MIME type: application/xml')
        } else if (contentType && contentType.includes('text/xml')) {
          console.log('   ⚠️  Alternative MIME type: text/xml (acceptable)')
        } else {
          console.log(`   ❌ Incorrect MIME type: ${contentType}`)
        }

        if (contentType && contentType.includes('charset=utf-8')) {
          console.log('   ✓ Correct charset: UTF-8')
        } else {
          console.log('   ⚠️  Missing or incorrect charset')
        }

        // Check other important headers
        console.log('\n🛡️  Security Headers:')
        if (res.headers['x-content-type-options']) {
          console.log(`   ✓ X-Content-Type-Options: ${res.headers['x-content-type-options']}`)
        } else {
          console.log('   ⚠️  Missing X-Content-Type-Options header')
        }

        if (res.headers['cache-control']) {
          console.log(`   ✓ Cache-Control: ${res.headers['cache-control']}`)
        } else {
          console.log('   ⚠️  Missing Cache-Control header')
        }

        // Sample content
        console.log('\n📄 Sample Content (first 200 chars):')
        console.log(data.substring(0, 200) + '...')

        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          content: data,
          isValidXML: data.startsWith('<?xml'),
          hasCorrectMimeType: contentType && contentType.includes('xml')
        })
      })
    })

    req.on('error', (err) => {
      console.error('❌ Request failed:', err.message)
      reject(err)
    })

    req.setTimeout(5000, () => {
      console.error('❌ Request timeout')
      req.destroy()
      reject(new Error('Request timeout'))
    })

    req.end()
  })
}

// Run test if script is executed directly
if (require.main === module) {
  testSitemapHeaders()
    .then(result => {
      console.log('\n🎯 Test Summary:')
      console.log(`   Status Code: ${result.statusCode === 200 ? '✅' : '❌'} ${result.statusCode}`)
      console.log(`   Valid XML: ${result.isValidXML ? '✅' : '❌'}`)
      console.log(`   Correct MIME Type: ${result.hasCorrectMimeType ? '✅' : '❌'}`)
      
      if (result.statusCode === 200 && result.isValidXML && result.hasCorrectMimeType) {
        console.log('\n🎉 All tests passed! Sitemap should display correctly in browsers.')
      } else {
        console.log('\n⚠️  Some issues detected. Check the details above.')
      }
    })
    .catch(err => {
      console.error('\n❌ Test failed:', err.message)
      console.log('\n💡 Make sure the development server is running with: npm run dev')
    })
}

module.exports = { testSitemapHeaders }
