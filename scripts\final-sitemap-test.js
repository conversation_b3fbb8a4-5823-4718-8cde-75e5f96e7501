/**
 * Final Sitemap Test - Comprehensive verification
 * Tests all aspects of sitemap.xml functionality
 */

const http = require('http')

async function testSitemap() {
  console.log('🔍 Final Sitemap Test - Comprehensive Verification\n')
  
  try {
    const result = await makeRequest()
    
    console.log('📊 Test Results:')
    console.log('================')
    
    // Status Code
    console.log(`Status Code: ${result.statusCode === 200 ? '✅' : '❌'} ${result.statusCode}`)
    
    // MIME Type
    const contentType = result.headers['content-type']
    const isXmlMime = contentType && (contentType.includes('text/xml') || contentType.includes('application/xml'))
    console.log(`MIME Type: ${isXmlMime ? '✅' : '❌'} ${contentType}`)
    
    // Charset
    const hasCharset = contentType && contentType.includes('charset=utf-8')
    console.log(`Charset: ${hasCharset ? '✅' : '❌'} ${hasCharset ? 'UTF-8' : 'Missing'}`)
    
    // Security Headers
    const hasXContentType = result.headers['x-content-type-options']
    console.log(`X-Content-Type-Options: ${hasXContentType ? '✅' : '❌'} ${hasXContentType || 'Missing'}`)
    
    // XML Structure
    const isValidXml = result.content.startsWith('<?xml version="1.0" encoding="UTF-8"?>')
    console.log(`XML Declaration: ${isValidXml ? '✅' : '❌'}`)
    
    const hasSitemapNamespace = result.content.includes('http://www.sitemaps.org/schemas/sitemap/0.9')
    console.log(`Sitemap Namespace: ${hasSitemapNamespace ? '✅' : '❌'}`)
    
    const hasHreflang = result.content.includes('hreflang=')
    console.log(`Hreflang Tags: ${hasHreflang ? '✅' : '❌'}`)
    
    // Content Analysis
    const urlCount = (result.content.match(/<url>/g) || []).length
    console.log(`URL Count: ${urlCount >= 12 ? '✅' : '❌'} ${urlCount} URLs`)
    
    const languageCount = new Set(
      (result.content.match(/hreflang="([^"]+)"/g) || [])
        .map(match => match.match(/hreflang="([^"]+)"/)[1])
        .filter(lang => lang !== 'x-default')
    ).size
    console.log(`Language Count: ${languageCount >= 12 ? '✅' : '❌'} ${languageCount} languages`)
    
    // Overall Assessment
    console.log('\n🎯 Overall Assessment:')
    console.log('======================')
    
    const allTestsPassed = 
      result.statusCode === 200 &&
      isXmlMime &&
      hasCharset &&
      hasXContentType &&
      isValidXml &&
      hasSitemapNamespace &&
      hasHreflang &&
      urlCount >= 12 &&
      languageCount >= 12
    
    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED! Sitemap is working correctly.')
      console.log('✅ Browsers should now display the sitemap as properly formatted XML.')
      console.log('✅ Search engines can properly index all language versions.')
    } else {
      console.log('❌ Some tests failed. Please check the issues above.')
    }
    
    // Sample URLs
    console.log('\n📋 Sample URLs from sitemap:')
    const urlMatches = result.content.match(/<loc>(.*?)<\/loc>/g)
    if (urlMatches) {
      urlMatches.slice(0, 5).forEach((match, index) => {
        const url = match.replace(/<\/?loc>/g, '')
        console.log(`   ${index + 1}. ${url}`)
      })
      if (urlMatches.length > 5) {
        console.log(`   ... and ${urlMatches.length - 5} more`)
      }
    }
    
    console.log('\n💡 Next Steps:')
    console.log('- Test in browser: http://localhost:3000/sitemap.xml')
    console.log('- Clear browser cache if needed')
    console.log('- Submit to Google Search Console after deployment')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.log('\n💡 Troubleshooting:')
    console.log('- Make sure the development server is running: npm run dev')
    console.log('- Check if port 3000 is available')
    console.log('- Verify serverMiddleware is enabled in nuxt.config.js')
  }
}

function makeRequest() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/sitemap.xml',
      method: 'GET',
      headers: {
        'User-Agent': 'Final-Test-Script/1.0',
        'Accept': 'application/xml,text/xml,*/*'
      }
    }

    const req = http.request(options, (res) => {
      let data = ''

      res.on('data', (chunk) => {
        data += chunk
      })

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          content: data
        })
      })
    })

    req.on('error', (err) => {
      reject(err)
    })

    req.setTimeout(5000, () => {
      req.destroy()
      reject(new Error('Request timeout'))
    })

    req.end()
  })
}

// Run test if script is executed directly
if (require.main === module) {
  testSitemap()
}

module.exports = { testSitemap }
