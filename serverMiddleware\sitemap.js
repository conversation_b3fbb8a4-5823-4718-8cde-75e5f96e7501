/**
 * Server Middleware for Sitemap.xml
 * Handles sitemap.xml requests with proper XML content-type headers
 */

const fs = require('fs')
const path = require('path')

// Configuration
const config = {
  hostname: process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://yugiohcardmaker.org',
  languages: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'],
  defaultLanguage: 'en'
}

// Page definitions
const pages = [
  {
    path: '/',
    priority: 1.0,
    changefreq: 'weekly',
    multilang: true,
    description: 'Yu-Gi-Oh! Card Maker - Professional Card Creator'
  }
]

/**
 * Generate XML sitemap dynamically
 */
function generateSitemap() {
  const currentDate = new Date().toISOString()
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n'
  xml += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n'

  pages.forEach(page => {
    // Generate URL entries for each language
    config.languages.forEach(lang => {
      xml += '  <url>\n'

      // Generate correct URL based on Nuxt.js i18n strategy
      // strategy: 'prefix_except_default' means default language doesn't need prefix
      let url
      if (lang === config.defaultLanguage) {
        url = `${config.hostname}${page.path}`
      } else {
        // Non-default languages use prefix
        url = page.path === '/'
          ? `${config.hostname}/${lang}/`
          : `${config.hostname}/${lang}${page.path}`
      }

      xml += `    <loc>${url}</loc>\n`
      xml += `    <lastmod>${currentDate}</lastmod>\n`
      xml += `    <changefreq>${page.changefreq}</changefreq>\n`
      xml += `    <priority>${page.priority}</priority>\n`

      // Add hreflang tags pointing to all language versions
      if (page.multilang) {
        config.languages.forEach(hrefLang => {
          let hrefUrl
          if (hrefLang === config.defaultLanguage) {
            hrefUrl = `${config.hostname}${page.path}`
          } else {
            hrefUrl = page.path === '/'
              ? `${config.hostname}/${hrefLang}/`
              : `${config.hostname}/${hrefLang}${page.path}`
          }
          xml += `    <xhtml:link rel="alternate" hreflang="${hrefLang}" href="${hrefUrl}" />\n`
        })

        // Add x-default pointing to default language
        xml += `    <xhtml:link rel="alternate" hreflang="x-default" href="${config.hostname}${page.path}" />\n`
      }

      xml += '  </url>\n'
    })
  })

  xml += '</urlset>\n'
  
  return xml
}

/**
 * Server middleware handler
 */
module.exports = function sitemapMiddleware(req, res, next) {
  // Only handle sitemap.xml requests
  if (req.url !== '/sitemap.xml') {
    return next()
  }

  console.log('Sitemap middleware: Handling sitemap.xml request')

  try {
    // Generate sitemap XML
    const sitemapXml = generateSitemap()

    // Set proper headers for XML content - this is crucial for browser recognition
    res.setHeader('Content-Type', 'application/xml; charset=utf-8')
    res.setHeader('X-Content-Type-Options', 'nosniff')
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET')
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

    // Force browser to treat as XML
    res.setHeader('Content-Disposition', 'inline; filename="sitemap.xml"')
    res.setHeader('Vary', 'Accept-Encoding')

    // Force no cache to ensure fresh content
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0')
    res.setHeader('Pragma', 'no-cache')
    res.setHeader('Expires', '0')
    res.setHeader('Last-Modified', new Date().toUTCString())

    if (process.env.NODE_ENV !== 'development') {
      res.setHeader('X-Robots-Tag', 'noindex') // Don't index the sitemap itself
    }

    console.log('Sitemap middleware: Serving fresh XML content')

    // Send the XML response
    res.statusCode = 200
    res.end(sitemapXml)
    console.log('Sitemap middleware: Successfully served dynamic sitemap')

  } catch (error) {
    console.error('Sitemap middleware: Error generating sitemap:', error)

    // Try to serve static sitemap as fallback
    const staticSitemapPath = path.join(__dirname, '../static/sitemap.xml')

    if (fs.existsSync(staticSitemapPath)) {
      try {
        const staticSitemap = fs.readFileSync(staticSitemapPath, 'utf8')

        // Set proper headers for XML content
        res.setHeader('Content-Type', 'application/xml; charset=utf-8')
        res.setHeader('X-Content-Type-Options', 'nosniff')

        if (process.env.NODE_ENV === 'development') {
          res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate')
        } else {
          res.setHeader('Cache-Control', 'public, max-age=3600')
          res.setHeader('X-Robots-Tag', 'noindex')
        }

        res.statusCode = 200
        res.end(staticSitemap)
        console.log('Sitemap middleware: Successfully served static sitemap fallback')
        return
      } catch (fallbackError) {
        console.error('Sitemap middleware: Error serving static sitemap:', fallbackError)
      }
    }

    // If all else fails, return 500 error
    res.statusCode = 500
    res.setHeader('Content-Type', 'text/plain')
    res.end('Internal Server Error: Unable to generate sitemap')
    console.log('Sitemap middleware: Failed to serve sitemap - returning 500 error')
  }
}
